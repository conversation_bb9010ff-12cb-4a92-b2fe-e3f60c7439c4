#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
月度工资异动处理系统 - 主应用入口

本文件是系统的主入口，负责启动应用程序和初始化各个模块。

功能:
1. 应用程序初始化
2. 异常处理和日志记录
3. 系统环境检查
4. 主窗口启动
"""

import sys
import os
import traceback
from pathlib import Path

from src.modules.system_config.config_manager import ConfigManager
from src.modules.data_storage.database_manager import DatabaseManager
from src.modules.data_storage.dynamic_table_manager import DynamicTableManager
from src.gui.prototype.prototype_main_window import PrototypeMainWindow

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

# 设置应用程序信息
APP_NAME = "月度工资异动处理系统"
APP_VERSION = "2.0.0-refactored"
APP_AUTHOR = "月度工资异动处理系统开发团队"

def setup_environment():
    """设置应用程序环境"""
    try:
        # 创建必要的目录
        directories = [
            'logs',
            'data',
            'output',
            'temp',
            'backup'
        ]
        
        for directory in directories:
            dir_path = project_root / directory
            dir_path.mkdir(exist_ok=True)
        
        # 设置环境变量
        os.environ['APP_NAME'] = APP_NAME
        os.environ['APP_VERSION'] = APP_VERSION
        os.environ['APP_ROOT'] = str(project_root)
        
        return True
        
    except Exception as e:
        print(f"环境设置失败: {e}")
        return False

def check_dependencies():
    """检查依赖项"""
    try:
        # 检查Python版本
        if sys.version_info < (3, 8):
            print("错误: 需要Python 3.8或更高版本")
            return False
        
        # 检查必要的包
        required_packages = [
            'PyQt5',
            'pandas',
            'openpyxl',
            'python-docx',
            'sqlite3'
        ]
        
        missing_packages = []
        for package in required_packages:
            try:
                if package == 'sqlite3':
                    import sqlite3
                elif package == 'PyQt5':
                    import PyQt5
                elif package == 'pandas':
                    import pandas
                elif package == 'openpyxl':
                    import openpyxl
                elif package == 'python-docx':
                    import docx
            except ImportError:
                missing_packages.append(package)
        
        if missing_packages:
            print(f"错误: 缺少以下必要的包: {', '.join(missing_packages)}")
            print("请运行: pip install -r requirements.txt")
            return False
        
        return True
        
    except Exception as e:
        print(f"依赖检查失败: {e}")
        return False

def setup_exception_handler():
    """设置全局异常处理器"""
    def handle_exception(exc_type, exc_value, exc_traceback):
        """全局异常处理函数"""
        if issubclass(exc_type, KeyboardInterrupt):
            # 允许Ctrl+C正常退出
            sys.__excepthook__(exc_type, exc_value, exc_traceback)
            return
        
        # 记录异常到日志文件
        error_msg = ''.join(traceback.format_exception(exc_type, exc_value, exc_traceback))
        
        # 写入日志文件
        log_file = project_root / 'logs' / 'error.log'
        try:
            with open(log_file, 'a', encoding='utf-8') as f:
                from datetime import datetime
                timestamp = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
                f.write(f"\n[{timestamp}] 未捕获的异常:\n{error_msg}\n")
        except Exception:
            pass  # 如果无法写入日志，忽略错误
        
        # 显示错误对话框
        try:
            from PyQt5.QtWidgets import QMessageBox, QApplication
            
            if QApplication.instance():
                msg_box = QMessageBox()
                msg_box.setIcon(QMessageBox.Critical)
                msg_box.setWindowTitle("系统错误")
                msg_box.setText("系统发生未处理的错误")
                msg_box.setDetailedText(error_msg)
                msg_box.setStandardButtons(QMessageBox.Ok)
                msg_box.exec_()
        except Exception:
            # 如果GUI不可用，打印到控制台
            print(f"系统错误: {error_msg}")
    
    # 设置异常处理器
    sys.excepthook = handle_exception

def create_application():
    """创建QApplication实例"""
    try:
        from PyQt5.QtWidgets import QApplication
        from PyQt5.QtCore import Qt
        from PyQt5.QtGui import QIcon, QFont
        
        # 设置高DPI支持 - 必须在QApplication创建之前设置
        QApplication.setAttribute(Qt.AA_EnableHighDpiScaling, True)
        QApplication.setAttribute(Qt.AA_UseHighDpiPixmaps, True)
        
        # 创建应用程序实例
        app = QApplication(sys.argv)
        
        # 设置应用程序属性
        app.setApplicationName(APP_NAME)
        app.setApplicationVersion(APP_VERSION)
        app.setOrganizationName(APP_AUTHOR)
        
        # 设置应用程序图标
        icon_path = project_root / 'resources' / 'icons' / 'app_icon.png'
        if icon_path.exists():
            app.setWindowIcon(QIcon(str(icon_path)))
        
        # 设置全局字体
        font = QFont("Microsoft YaHei", 9)
        app.setFont(font)
        
        # 设置样式
        app.setStyle('Fusion')
        
        return app
        
    except Exception as e:
        print(f"创建应用程序失败: {e}")
        return None

def create_main_window(config_manager, db_manager, dynamic_table_manager):
    """创建主窗口"""
    try:
        # 重构后，我们统一使用 PrototypeMainWindow
        main_window = PrototypeMainWindow(
            config_manager=config_manager,
            db_manager=db_manager,
            dynamic_table_manager=dynamic_table_manager
        )
        
        main_window.show()
        return main_window
        
    except Exception as e:
        print(f"创建主窗口失败: {e}")
        traceback.print_exc()
        return None

def setup_app_logging():
    """设置日志系统"""
    try:
        from src.utils.log_config import setup_logger
        logger = setup_logger(__name__)
        logger.info(f"{APP_NAME} v{APP_VERSION} 启动")
        return logger
    except Exception as e:
        print(f"日志系统初始化失败: {e}")
        return None

def show_splash_screen(app):
    """显示启动画面"""
    try:
        from PyQt5.QtWidgets import QSplashScreen, QLabel
        from PyQt5.QtCore import Qt, QTimer
        from PyQt5.QtGui import QPixmap, QFont
        
        # 创建启动画面
        splash_pix = QPixmap(400, 300)
        splash_pix.fill(Qt.white)
        
        splash = QSplashScreen(splash_pix, Qt.WindowStaysOnTopHint)
        
        # 添加启动信息
        splash.showMessage(
            f"{APP_NAME}\n版本 {APP_VERSION}\n正在启动...",
            Qt.AlignCenter | Qt.AlignBottom,
            Qt.black
        )
        
        splash.show()
        app.processEvents()
        
        return splash
        
    except Exception as e:
        print(f"启动画面创建失败: {e}")
        return None

def show_version():
    """显示版本信息"""
    print(f"""
{APP_NAME} v{APP_VERSION}

开发团队: {APP_AUTHOR}
Python版本: {sys.version}
系统平台: {sys.platform}

项目目录: {project_root}
    """)

def show_help():
    """显示帮助信息"""
    print(f"""
{APP_NAME} v{APP_VERSION}

用法:
    python main.py [选项]

选项:
    -h, --help      显示此帮助信息
    -v, --version   显示版本信息
    --test          运行测试
    --check         检查系统环境和依赖

示例:
    python main.py              # 正常启动程序
    python main.py --test       # 运行测试
    python main.py --check      # 检查环境
    """)

if __name__ == "__main__":
    # 确保这些关键设置在任何情况下都先执行
    if not setup_environment():
        sys.exit(1)

    logger = setup_app_logging()
    if not logger:
        sys.exit(1)

    setup_exception_handler()

    # 主应用逻辑
    try:
        print(f"正在启动 {APP_NAME} v{APP_VERSION}...")
        
        # 创建Qt应用实例
        app = create_application()
        if not app:
            logger.error("无法创建应用程序实例，退出")
            sys.exit(1)

        # 初始化核心管理器
        logger.info("初始化核心管理器...")
        config_manager = ConfigManager()
        db_manager = DatabaseManager(config_manager=config_manager)
        dynamic_table_manager = DynamicTableManager(db_manager=db_manager, config_manager=config_manager)
        logger.info("核心管理器初始化完成。")

        # 创建并显示主窗口
        # 这个变量必须在这里定义，以防止窗口被垃圾回收
        main_window = create_main_window(config_manager, db_manager, dynamic_table_manager)
        if not main_window:
            logger.error("无法创建主窗口，应用程序退出")
            sys.exit(1)

        # 启动事件循环
        logger.info("启动应用程序事件循环...")
        exit_code = app.exec_()
        logger.info(f"应用程序正常退出，退出代码: {exit_code}")
        sys.exit(exit_code)

    except SystemExit as e:
        # 捕获sys.exit()，确保正常退出
        logger.info(f"应用程序通过 SystemExit 退出，代码: {e.code}")
        # 不需要重新调用sys.exit(e.code)，因为原始的sys.exit()已经触发了退出流程

    except Exception as e:
        error_msg = f"应用程序启动时发生致命错误: {e}"
        if logger:
            logger.critical(error_msg, exc_info=True)
        else:
            print(error_msg)
            traceback.print_exc()
        sys.exit(1) 